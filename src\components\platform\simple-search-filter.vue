<template>
  <view class="search-filter-wrapper">
    <!-- 搜索框(因为界面上没有添加任务ID,所以去除) -->
    <!-- <view class="search-section">
      <up-search
        v-model="searchKeyword"
        :placeholder="placeholder"
        :show-action="false"
        bg-color="#f5f5f5"
        border-color="transparent"
        @search="handleSearch"
        @input="handleSearchInput"
        @clear="handleSearchClear"
      />
    </view> -->

    <!-- 时间筛选 -->
    <view class="time-filter-section" v-if="showTimeFilter">
      <view class="time-filter-title">时间筛选：</view>
      <view class="time-filter-content">
        <view class="time-item">
          <up-datetime-picker
            v-model="startTimeValue"
            mode="date"
            placeholder='选择开始时间'
            @confirm="handleStartTimeChange"
            hasInput
            :minDate="minDate"
            :maxDate="maxDate"
            :inputProps="{
              border: 'surround',
              shape: 'square',
              inputAlign: 'center',
              suffixIcon: 'calendar'
            }"
          >
          </up-datetime-picker>
        </view>
        <view class="time-item">
          <up-datetime-picker
            v-model="endTimeValue"
            mode="date"
            placeholder='选择结束时间'
            @confirm="handleEndTimeChange"
            hasInput
            :minDate="minDate"
            :maxDate="maxDate"
            :inputProps="{
              border: 'surround',
              shape: 'square',
              inputAlign: 'center',
              suffixIcon: 'calendar'
            }"
          >
          </up-datetime-picker>
        </view>
      </view>
    </view>

    <!-- 快捷筛选标签 -->
    <view class="filter-section" v-if="filterTags.length > 0">
      <view class="filter-title">状态筛选：</view>
      <scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
        <view class="filter-tags">
          <view
            v-for="(tag, index) in filterTags"
            :key="index"
            class="filter-tag"
            :class="{ active: activeFilter === tag.value }"
            @click="handleFilterClick(tag.value)"
          >
            {{ tag.label }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 搜索结果统计 -->
    <view class="result-section" v-if="showResultCount">
      <text class="result-text">共 {{ resultCount }} 个结果</text>
      <view class="clear-btn" v-if="hasActiveFilters" @click="handleClearAll">
        <text class="clear-text">清空</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'

// Props
const props = defineProps({
  // 搜索关键词
  modelValue: {
    type: String,
    default: ''
  },
  // 搜索占位符
  placeholder: {
    type: String,
    default: '请输入搜索关键词...'
  },
  // 筛选标签
  filterTags: {
    type: Array,
    default: () => []
  },
  // 当前激活的筛选
  activeFilter: {
    type: [String, Number],
    default: ''
  },
  // 是否显示结果统计
  showResultCount: {
    type: Boolean,
    default: false
  },
  // 结果数量
  resultCount: {
    type: Number,
    default: 0
  },
  // 是否显示时间筛选
  showTimeFilter: {
    type: Boolean,
    default: false
  },
  // 开始时间
  startTime: {
    type: String,
    default: ''
  },
  // 结束时间
  endTime: {
    type: String,
    default: ''
  },
  // 开始时间占位符
  startTimePlaceholder: {
    type: String,
    default: '选择开始时间'
  },
  // 结束时间占位符
  endTimePlaceholder: {
    type: String,
    default: '选择结束时间'
  }
})

// Emits
const emit = defineEmits([
  'update:modelValue',
  'update:activeFilter',
  'update:startTime',
  'update:endTime',
  'search',
  'filter-change',
  'time-change',
  'clear-all'
])

// 响应式数据
const searchKeyword = ref(props.modelValue)
const currentFilter = ref(props.activeFilter)
const startTimeValue = ref(props.startTime)
const endTimeValue = ref(props.endTime)

// 计算最小日期（3年前）和最大日期（5年后）
const minDate = computed(() => {
  const date = new Date()
  date.setFullYear(date.getFullYear() - 3)
  return date.getTime()
})

const maxDate = computed(() => {
  const date = new Date()
  date.setFullYear(date.getFullYear() + 5)
  return date.getTime()
})

// 计算是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return searchKeyword.value.trim() !== '' ||
         currentFilter.value !== '' ||
         startTimeValue.value !== '' ||
         endTimeValue.value !== ''
})

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  searchKeyword.value = newVal
})

watch(() => props.activeFilter, (newVal) => {
  currentFilter.value = newVal
})

watch(() => props.startTime, (newVal) => {
  startTimeValue.value = newVal
})

watch(() => props.endTime, (newVal) => {
  endTimeValue.value = newVal
})

// 监听内部值变化，同步到外部
watch(searchKeyword, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(currentFilter, (newVal) => {
  emit('update:activeFilter', newVal)
})

watch(startTimeValue, (newVal) => {
  emit('update:startTime', newVal)
})

watch(endTimeValue, (newVal) => {
  emit('update:endTime', newVal)
})



// 筛选处理
const handleFilterClick = (value) => {
  // 如果点击的是当前激活的筛选，则取消选择
  currentFilter.value = currentFilter.value === value ? '' : value
  emit('filter-change', currentFilter.value)
  emitSearchEvent()
}

// 时间处理函数
const handleStartTimeChange = (value) => {
  console.log('开始时间变化:', value)

  // 直接使用时间戳，不转换为UTC字符串
  if (typeof value === 'object' && value !== null && value.value) {
    console.log('提取时间戳:', value.value)
    startTimeValue.value = value.value  // 直接使用时间戳

    emit('time-change', {
      startTime: formatToUTC(value),  // 只在emit时转换为UTC
      endTime: endTimeValue.value ? formatToUTC({value: endTimeValue.value, mode: 'date'}) : ''
    })
    emitSearchEvent()
  }
}

const handleEndTimeChange = (value) => {
  console.log('结束时间变化:', value)

  // 直接使用时间戳，不转换为UTC字符串
  if (typeof value === 'object' && value !== null && value.value) {
    console.log('提取时间戳:', value.value)
    endTimeValue.value = value.value  // 直接使用时间戳

    emit('time-change', {
      startTime: startTimeValue.value ? formatToUTC({value: startTimeValue.value, mode: 'date'}) : '',
      endTime: formatToUTC(value)  // 只在emit时转换为UTC
    })
    emitSearchEvent()
  }
}



// 格式化时间为 UTC 格式
const formatToUTC = (dateTime) => {
  if (!dateTime) return ''

  try {
    console.log('接收到的日期数据:', dateTime, typeof dateTime)

    let date

    // 处理 up-datetime-picker 返回的对象格式: {value: 1735660800000, mode: "date"}
    if (typeof dateTime === 'object' && dateTime !== null && dateTime.value) {
      date = new Date(dateTime.value)
    } else {
      console.error('无法识别的日期格式:', dateTime)
      return ''
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的日期:', dateTime)
      return ''
    }

    console.log('格式化后的日期:', date.toISOString())
    return date.toISOString().replace('.000Z', 'Z')
  } catch (error) {
    console.error('时间格式化错误:', error, dateTime)
    return ''
  }
}





// 清空所有筛选
const handleClearAll = () => {
  searchKeyword.value = ''
  currentFilter.value = ''
  startTimeValue.value = ''
  endTimeValue.value = ''
  emit('clear-all')
  emitSearchEvent()
}

// 发送搜索事件
const emitSearchEvent = () => {
  emit('search', {
    keyword: searchKeyword.value,
    filter: currentFilter.value,
    startTime: startTimeValue.value,
    endTime: endTimeValue.value
  })
}
</script>

<style lang="scss" scoped>
.search-filter-wrapper {
  background-color: #fff;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .search-section {
    margin-bottom: 20rpx;
  }

  .time-filter-section {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    overflow: hidden;

    .time-filter-title {
      font-size: 28rpx;
      color: #666;
      margin-right: 16rpx;
      white-space: nowrap;
      flex-shrink: 0;
    }

    .time-filter-content {
      display: flex;
      gap: 20rpx;
      flex: 1;

      .time-item {
        flex: 1;
      }
    }
  }

  .filter-section {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    overflow: hidden;

    .filter-title {
      font-size: 28rpx;
      color: #666;
      margin-right: 16rpx;
      white-space: nowrap;
      flex-shrink: 0;
    }

    .filter-scroll {
      flex: 1;
      overflow-x: auto;
      
      .filter-tags {
        display: flex;
        gap: 12rpx;
        padding-right: 20rpx;
        min-width: max-content;

        .filter-tag {
          padding: 12rpx 20rpx;
          background-color: #f5f5f5;
          border-radius: 32rpx;
          font-size: 24rpx;
          color: #666;
          white-space: nowrap;
          transition: all 0.3s ease;
          cursor: pointer;
          min-width: fit-content;
          flex-shrink: 0;

          &.active {
            background-color: #667eea;
            color: #fff;
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }
  }

  .result-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16rpx;
    border-top: 1rpx solid #f0f0f0;

    .result-text {
      font-size: 26rpx;
      color: #999;
    }

    .clear-btn {
      .clear-text {
        font-size: 26rpx;
        color: #667eea;
        cursor: pointer;

        &:active {
          color: #5a6fd8;
        }
      }
    }
  }
}
</style>
